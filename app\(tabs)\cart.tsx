import React from 'react';
import { View, StyleSheet, ScrollView, TouchableOpacity, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Button } from '@/components/ui/Button';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useCart } from '@/context';
import { useThemeColor } from '@/hooks/useThemeColor';

export default function CartScreen() {
  const { 
    items, 
    total, 
    deliveryFee, 
    tax, 
    grandTotal, 
    updateQuantity, 
    removeFromCart,
    clearCart 
  } = useCart();

  const backgroundColor = useThemeColor({}, 'background');
  const textColor = useThemeColor({}, 'text');

  const handleQuantityChange = (itemId: string, newQuantity: number) => {
    if (newQuantity <= 0) {
      Alert.alert(
        'Remove Item',
        'Are you sure you want to remove this item from your cart?',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Remove', style: 'destructive', onPress: () => removeFromCart(itemId) }
        ]
      );
    } else {
      updateQuantity(itemId, newQuantity);
    }
  };

  const handleClearCart = () => {
    Alert.alert(
      'Clear Cart',
      'Are you sure you want to remove all items from your cart?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Clear', style: 'destructive', onPress: clearCart }
      ]
    );
  };

  const handleCheckout = () => {
    if (items.length === 0) {
      Alert.alert('Empty Cart', 'Please add some items to your cart first.');
      return;
    }
    router.push('/checkout');
  };

  if (items.length === 0) {
    return (
      <SafeAreaView style={styles.container}>
        <ThemedView style={styles.header}>
          <ThemedText style={styles.title}>Cart</ThemedText>
        </ThemedView>
        
        <ThemedView style={styles.emptyContainer}>
          <ThemedText style={styles.emptyEmoji}>🛒</ThemedText>
          <ThemedText style={styles.emptyTitle}>Your cart is empty</ThemedText>
          <ThemedText style={styles.emptySubtitle}>
            Add some delicious food to get started!
          </ThemedText>
          <Button
            title="Browse Menu"
            onPress={() => router.push('/(tabs)/menu')}
            style={styles.browseButton}
          />
        </ThemedView>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ThemedView style={styles.header}>
        <ThemedText style={styles.title}>Cart ({items.length})</ThemedText>
        <TouchableOpacity onPress={handleClearCart}>
          <ThemedText style={styles.clearButton}>Clear All</ThemedText>
        </TouchableOpacity>
      </ThemedView>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {items.map((item) => (
          <ThemedView key={item.id} style={[styles.cartItem, { backgroundColor }]}>
            <View style={styles.itemInfo}>
              <ThemedText style={styles.itemName}>{item.foodItem.name}</ThemedText>
              <ThemedText style={styles.itemPrice}>
                ${item.foodItem.price.toFixed(2)} each
              </ThemedText>
              {item.specialInstructions && (
                <ThemedText style={styles.specialInstructions}>
                  Note: {item.specialInstructions}
                </ThemedText>
              )}
              {item.addOns && item.addOns.length > 0 && (
                <ThemedText style={styles.addOns}>
                  Add-ons: {item.addOns.map(addon => addon.name).join(', ')}
                </ThemedText>
              )}
            </View>

            <View style={styles.quantityContainer}>
              <TouchableOpacity
                style={styles.quantityButton}
                onPress={() => handleQuantityChange(item.id, item.quantity - 1)}
              >
                <IconSymbol name="minus" size={16} color={textColor} />
              </TouchableOpacity>
              
              <ThemedText style={styles.quantity}>{item.quantity}</ThemedText>
              
              <TouchableOpacity
                style={styles.quantityButton}
                onPress={() => handleQuantityChange(item.id, item.quantity + 1)}
              >
                <IconSymbol name="plus" size={16} color={textColor} />
              </TouchableOpacity>
            </View>

            <View style={styles.itemTotal}>
              <ThemedText style={styles.itemTotalPrice}>
                ${((item.foodItem.price + (item.addOns?.reduce((sum, addon) => sum + addon.price, 0) || 0)) * item.quantity).toFixed(2)}
              </ThemedText>
            </View>
          </ThemedView>
        ))}
      </ScrollView>

      {/* Order Summary */}
      <ThemedView style={styles.summary}>
        <View style={styles.summaryRow}>
          <ThemedText style={styles.summaryLabel}>Subtotal</ThemedText>
          <ThemedText style={styles.summaryValue}>${total.toFixed(2)}</ThemedText>
        </View>
        
        <View style={styles.summaryRow}>
          <ThemedText style={styles.summaryLabel}>Delivery Fee</ThemedText>
          <ThemedText style={styles.summaryValue}>
            {deliveryFee === 0 ? 'FREE' : `$${deliveryFee.toFixed(2)}`}
          </ThemedText>
        </View>
        
        <View style={styles.summaryRow}>
          <ThemedText style={styles.summaryLabel}>Tax</ThemedText>
          <ThemedText style={styles.summaryValue}>${tax.toFixed(2)}</ThemedText>
        </View>
        
        <View style={[styles.summaryRow, styles.totalRow]}>
          <ThemedText style={styles.totalLabel}>Total</ThemedText>
          <ThemedText style={styles.totalValue}>${grandTotal.toFixed(2)}</ThemedText>
        </View>

        <Button
          title="Proceed to Checkout"
          onPress={handleCheckout}
          style={styles.checkoutButton}
        />
      </ThemedView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
  },
  clearButton: {
    color: '#ff4444',
    fontSize: 16,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  cartItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  itemInfo: {
    flex: 1,
    marginRight: 16,
  },
  itemName: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  itemPrice: {
    fontSize: 14,
    opacity: 0.7,
    marginBottom: 4,
  },
  specialInstructions: {
    fontSize: 12,
    fontStyle: 'italic',
    opacity: 0.8,
    marginBottom: 2,
  },
  addOns: {
    fontSize: 12,
    opacity: 0.8,
  },
  quantityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
  },
  quantityButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  quantity: {
    fontSize: 16,
    fontWeight: 'bold',
    marginHorizontal: 16,
    minWidth: 24,
    textAlign: 'center',
  },
  itemTotal: {
    alignItems: 'flex-end',
  },
  itemTotalPrice: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#007AFF',
  },
  summary: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  summaryLabel: {
    fontSize: 16,
  },
  summaryValue: {
    fontSize: 16,
    fontWeight: '500',
  },
  totalRow: {
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    paddingTop: 8,
    marginTop: 8,
    marginBottom: 16,
  },
  totalLabel: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  totalValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#007AFF',
  },
  checkoutButton: {
    marginTop: 8,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyEmoji: {
    fontSize: 64,
    marginBottom: 16,
  },
  emptyTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
    textAlign: 'center',
  },
  emptySubtitle: {
    fontSize: 16,
    opacity: 0.7,
    textAlign: 'center',
    marginBottom: 24,
  },
  browseButton: {
    minWidth: 200,
  },
});
