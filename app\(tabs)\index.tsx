import { router } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { Alert, RefreshControl, ScrollView, StyleSheet, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { BannerSlider } from '@/components/food/BannerSlider';
import { CategoryGrid } from '@/components/food/CategoryGrid';
import { RestaurantCard } from '@/components/food/RestaurantCard';
import { useAuth } from '@/context';
import { foodAPI } from '@/services/api';
import { Banner, Category, Restaurant } from '@/types';

export default function HomeScreen() {
  const { user } = useAuth();
  const [banners, setBanners] = useState<Banner[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [restaurant, setRestaurant] = useState<Restaurant | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const loadData = async () => {
    try {
      const [bannersResponse, categoriesResponse, restaurantResponse] = await Promise.all([
        foodAPI.getBanners(),
        foodAPI.getCategories(),
        foodAPI.getRestaurant()
      ]);

      if (bannersResponse.success) setBanners(bannersResponse.data);
      if (categoriesResponse.success) setCategories(categoriesResponse.data);
      if (restaurantResponse.success) setRestaurant(restaurantResponse.data);
    } catch (error) {
      Alert.alert('Error', 'Failed to load data. Please try again.');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    loadData();
  }, []);

  const handleRefresh = () => {
    setRefreshing(true);
    loadData();
  };

  const handleCategoryPress = (category: Category) => {
    // Navigate to menu with category filter
    router.push('/(tabs)/menu');
  };

  const handleRestaurantPress = () => {
    // Navigate to menu
    router.push('/(tabs)/menu');
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <ThemedView style={styles.header}>
          <ThemedText style={styles.greeting}>
            Hello, {user?.name || 'Food Lover'}! 👋
          </ThemedText>
          <ThemedText style={styles.subtitle}>
            What would you like to eat today?
          </ThemedText>
        </ThemedView>

        {/* Banner Slider */}
        {banners.length > 0 && (
          <BannerSlider banners={banners} />
        )}

        {/* Categories */}
        {categories.length > 0 && (
          <CategoryGrid
            categories={categories}
            onCategoryPress={handleCategoryPress}
          />
        )}

        {/* Restaurant Info */}
        {restaurant && (
          <View style={styles.restaurantSection}>
            <ThemedText style={styles.sectionTitle}>Our Restaurant</ThemedText>
            <RestaurantCard
              restaurant={restaurant}
              onPress={handleRestaurantPress}
            />
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    padding: 16,
    paddingTop: 8,
  },
  greeting: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    opacity: 0.7,
  },
  restaurantSection: {
    paddingBottom: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
    paddingHorizontal: 16,
  },
});
