import React from 'react';
import { View, StyleSheet, ScrollView, TouchableOpacity, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Button } from '@/components/ui/Button';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useAuth } from '@/context';
import { useThemeColor } from '@/hooks/useThemeColor';

export default function ProfileScreen() {
  const { user, logout } = useAuth();
  const backgroundColor = useThemeColor({}, 'background');
  const textColor = useThemeColor({}, 'text');

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Logout', 
          style: 'destructive', 
          onPress: async () => {
            await logout();
            router.replace('/(auth)/login');
          }
        }
      ]
    );
  };

  const menuItems = [
    {
      id: '1',
      title: 'Edit Profile',
      icon: 'person',
      onPress: () => Alert.alert('Coming Soon', 'Profile editing will be available soon!')
    },
    {
      id: '2',
      title: 'Delivery Addresses',
      icon: 'location',
      onPress: () => Alert.alert('Coming Soon', 'Address management will be available soon!')
    },
    {
      id: '3',
      title: 'Payment Methods',
      icon: 'creditcard',
      onPress: () => Alert.alert('Coming Soon', 'Payment methods will be available soon!')
    },
    {
      id: '4',
      title: 'Order History',
      icon: 'bag',
      onPress: () => router.push('/(tabs)/orders')
    },
    {
      id: '5',
      title: 'Settings',
      icon: 'gear',
      onPress: () => Alert.alert('Coming Soon', 'Settings will be available soon!')
    }
  ];

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Profile Header */}
        <ThemedView style={styles.profileHeader}>
          <View style={styles.avatarContainer}>
            <ThemedText style={styles.avatarText}>
              {user?.name?.charAt(0).toUpperCase() || 'U'}
            </ThemedText>
          </View>
          <ThemedText style={styles.userName}>{user?.name || 'User'}</ThemedText>
          <ThemedText style={styles.userEmail}>{user?.email || '<EMAIL>'}</ThemedText>
        </ThemedView>

        {/* Menu Items */}
        <ThemedView style={styles.menuSection}>
          {menuItems.map((item) => (
            <TouchableOpacity
              key={item.id}
              style={[styles.menuItem, { backgroundColor }]}
              onPress={item.onPress}
              activeOpacity={0.8}
            >
              <View style={styles.menuItemLeft}>
                <IconSymbol name={item.icon as any} size={24} color={textColor} />
                <ThemedText style={styles.menuItemText}>{item.title}</ThemedText>
              </View>
              <IconSymbol name="chevron.right" size={20} color={textColor} />
            </TouchableOpacity>
          ))}
        </ThemedView>

        {/* Logout Button */}
        <View style={styles.logoutSection}>
          <Button
            title="Logout"
            onPress={handleLogout}
            variant="outline"
            style={styles.logoutButton}
          />
        </View>

        {/* App Info */}
        <ThemedView style={styles.appInfo}>
          <ThemedText style={styles.appInfoText}>FoodDelivery v1.0.0</ThemedText>
          <ThemedText style={styles.appInfoText}>Made with ❤️ for food lovers</ThemedText>
        </ThemedView>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  profileHeader: {
    alignItems: 'center',
    padding: 32,
  },
  avatarContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#007AFF',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  avatarText: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#ffffff',
  },
  userName: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  userEmail: {
    fontSize: 16,
    opacity: 0.7,
  },
  menuSection: {
    paddingHorizontal: 16,
    marginBottom: 32,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderRadius: 12,
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  menuItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  menuItemText: {
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 16,
  },
  logoutSection: {
    paddingHorizontal: 16,
    marginBottom: 32,
  },
  logoutButton: {
    borderColor: '#ff4444',
  },
  appInfo: {
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingBottom: 32,
  },
  appInfoText: {
    fontSize: 14,
    opacity: 0.6,
    marginBottom: 4,
  },
});
