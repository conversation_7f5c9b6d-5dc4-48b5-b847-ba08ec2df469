import { router } from 'expo-router';
import React, { useState } from 'react';
import { ScrollView, StyleSheet, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Button } from '@/components/ui/Button';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useAuth, useCart } from '@/context';
import { useThemeColor } from '@/hooks/useThemeColor';

export default function CheckoutScreen() {
  const { items, total, deliveryFee, tax, grandTotal, clearCart } = useCart();
  const { user } = useAuth();
  const [selectedPayment, setSelectedPayment] = useState('card');
  const [isPlacingOrder, setIsPlacingOrder] = useState(false);

  const backgroundColor = useThemeColor({}, 'background');
  const textColor = useThemeColor({}, 'text');

  const paymentMethods = [
    { id: 'card', name: 'Credit Card', icon: 'creditcard' },
    { id: 'cash', name: 'Cash on Delivery', icon: 'bag' },
  ];

  const handlePlaceOrder = async () => {
    setIsPlacingOrder(true);

    // Simulate order placement
    setTimeout(() => {
      setIsPlacingOrder(false);
      clearCart();

      // Generate order ID
      const orderId = 'ORD' + Date.now().toString().slice(-6);

      // Navigate to thank you page
      router.replace(`/thank-you?orderId=${orderId}`);
    }, 2000);
  };

  if (items.length === 0) {
    router.replace('/(tabs)/cart');
    return null;
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <ThemedView style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <IconSymbol name="arrow.left" size={24} color={textColor} />
        </TouchableOpacity>
        <ThemedText style={styles.title}>Checkout</ThemedText>
        <View style={{ width: 24 }} />
      </ThemedView>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Delivery Address */}
        <ThemedView style={styles.section}>
          <ThemedText style={styles.sectionTitle}>Delivery Address</ThemedText>
          <TouchableOpacity style={[styles.addressCard, { backgroundColor }]}>
            <IconSymbol name="location" size={20} color={textColor} />
            <View style={styles.addressInfo}>
              <ThemedText style={styles.addressTitle}>Home</ThemedText>
              <ThemedText style={styles.addressText}>
                123 Main Street, City, State 12345
              </ThemedText>
            </View>
            <IconSymbol name="chevron.right" size={20} color={textColor} />
          </TouchableOpacity>
        </ThemedView>

        {/* Order Summary */}
        <ThemedView style={styles.section}>
          <ThemedText style={styles.sectionTitle}>Order Summary</ThemedText>
          {items.map((item) => (
            <View key={item.id} style={styles.orderItem}>
              <View style={styles.orderItemInfo}>
                <ThemedText style={styles.orderItemName}>
                  {item.quantity}x {item.foodItem.name}
                </ThemedText>
                <ThemedText style={styles.orderItemPrice}>
                  ${((item.foodItem.price + (item.addOns?.reduce((sum, addon) => sum + addon.price, 0) || 0)) * item.quantity).toFixed(2)}
                </ThemedText>
              </View>
            </View>
          ))}
        </ThemedView>

        {/* Payment Method */}
        <ThemedView style={styles.section}>
          <ThemedText style={styles.sectionTitle}>Payment Method</ThemedText>
          {paymentMethods.map((method) => (
            <TouchableOpacity
              key={method.id}
              style={[
                styles.paymentMethod,
                { backgroundColor },
                selectedPayment === method.id && styles.paymentMethodSelected
              ]}
              onPress={() => setSelectedPayment(method.id)}
            >
              <View style={styles.paymentMethodLeft}>
                <IconSymbol name={method.icon as any} size={20} color={textColor} />
                <ThemedText style={styles.paymentMethodText}>{method.name}</ThemedText>
              </View>
              <View style={[
                styles.radioButton,
                selectedPayment === method.id && styles.radioButtonSelected
              ]}>
                {selectedPayment === method.id && (
                  <IconSymbol name="checkmark" size={12} color="#ffffff" />
                )}
              </View>
            </TouchableOpacity>
          ))}
        </ThemedView>

        {/* Bill Details */}
        <ThemedView style={styles.section}>
          <ThemedText style={styles.sectionTitle}>Bill Details</ThemedText>
          <View style={styles.billRow}>
            <ThemedText style={styles.billLabel}>Subtotal</ThemedText>
            <ThemedText style={styles.billValue}>${total.toFixed(2)}</ThemedText>
          </View>
          <View style={styles.billRow}>
            <ThemedText style={styles.billLabel}>Delivery Fee</ThemedText>
            <ThemedText style={styles.billValue}>
              {deliveryFee === 0 ? 'FREE' : `$${deliveryFee.toFixed(2)}`}
            </ThemedText>
          </View>
          <View style={styles.billRow}>
            <ThemedText style={styles.billLabel}>Tax</ThemedText>
            <ThemedText style={styles.billValue}>${tax.toFixed(2)}</ThemedText>
          </View>
          <View style={[styles.billRow, styles.totalRow]}>
            <ThemedText style={styles.totalLabel}>Total</ThemedText>
            <ThemedText style={styles.totalValue}>${grandTotal.toFixed(2)}</ThemedText>
          </View>
        </ThemedView>
      </ScrollView>

      {/* Place Order Button */}
      <ThemedView style={styles.footer}>
        <Button
          title={isPlacingOrder ? 'Placing Order...' : `Place Order • $${grandTotal.toFixed(2)}`}
          onPress={handlePlaceOrder}
          loading={isPlacingOrder}
          style={styles.placeOrderButton}
        />
      </ThemedView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
  },
  section: {
    padding: 16,
    marginBottom: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  addressCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  addressInfo: {
    flex: 1,
    marginLeft: 12,
  },
  addressTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  addressText: {
    fontSize: 14,
    opacity: 0.7,
  },
  orderItem: {
    marginBottom: 8,
  },
  orderItemInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  orderItemName: {
    fontSize: 16,
    flex: 1,
  },
  orderItemPrice: {
    fontSize: 16,
    fontWeight: '600',
  },
  paymentMethod: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderRadius: 12,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: 'transparent',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  paymentMethodSelected: {
    borderColor: '#007AFF',
  },
  paymentMethodLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  paymentMethodText: {
    fontSize: 16,
    marginLeft: 12,
  },
  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#cccccc',
    justifyContent: 'center',
    alignItems: 'center',
  },
  radioButtonSelected: {
    backgroundColor: '#007AFF',
    borderColor: '#007AFF',
  },
  billRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  billLabel: {
    fontSize: 16,
  },
  billValue: {
    fontSize: 16,
    fontWeight: '500',
  },
  totalRow: {
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    paddingTop: 8,
    marginTop: 8,
  },
  totalLabel: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  totalValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#007AFF',
  },
  footer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  placeOrderButton: {
    minHeight: 56,
  },
});
