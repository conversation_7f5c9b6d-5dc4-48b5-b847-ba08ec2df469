import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView, Image, TouchableOpacity, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useLocalSearchParams, router } from 'expo-router';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useCart } from '@/context';
import { useThemeColor } from '@/hooks/useThemeColor';
import { foodAPI } from '@/services/api';
import { FoodItem, AddOn } from '@/types';

export default function ProductDetailScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const { addToCart } = useCart();
  
  const [foodItem, setFoodItem] = useState<FoodItem | null>(null);
  const [quantity, setQuantity] = useState(1);
  const [specialInstructions, setSpecialInstructions] = useState('');
  const [selectedAddOns, setSelectedAddOns] = useState<AddOn[]>([]);
  const [loading, setLoading] = useState(true);

  const backgroundColor = useThemeColor({}, 'background');
  const textColor = useThemeColor({}, 'text');

  // Mock add-ons data
  const availableAddOns: AddOn[] = [
    { id: '1', name: 'Extra Cheese', price: 2.50 },
    { id: '2', name: 'Extra Sauce', price: 1.00 },
    { id: '3', name: 'Bacon', price: 3.00 },
    { id: '4', name: 'Mushrooms', price: 1.50 },
  ];

  useEffect(() => {
    if (id) {
      loadFoodItem();
    }
  }, [id]);

  const loadFoodItem = async () => {
    try {
      const response = await foodAPI.getFoodItem(id!);
      if (response.success) {
        setFoodItem(response.data);
      } else {
        Alert.alert('Error', 'Food item not found');
        router.back();
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to load food item');
      router.back();
    } finally {
      setLoading(false);
    }
  };

  const handleQuantityChange = (delta: number) => {
    const newQuantity = quantity + delta;
    if (newQuantity >= 1) {
      setQuantity(newQuantity);
    }
  };

  const toggleAddOn = (addOn: AddOn) => {
    setSelectedAddOns(prev => {
      const exists = prev.find(item => item.id === addOn.id);
      if (exists) {
        return prev.filter(item => item.id !== addOn.id);
      } else {
        return [...prev, addOn];
      }
    });
  };

  const calculateTotal = () => {
    if (!foodItem) return 0;
    const basePrice = foodItem.price;
    const addOnsPrice = selectedAddOns.reduce((sum, addOn) => sum + addOn.price, 0);
    return (basePrice + addOnsPrice) * quantity;
  };

  const handleAddToCart = () => {
    if (!foodItem) return;
    
    addToCart(foodItem, quantity, specialInstructions, selectedAddOns);
    
    Alert.alert(
      'Added to Cart! 🎉',
      `${quantity}x ${foodItem.name} has been added to your cart.`,
      [
        { text: 'Continue Shopping', onPress: () => router.back() },
        { text: 'View Cart', onPress: () => router.push('/(tabs)/cart') }
      ]
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <ThemedView style={styles.loadingContainer}>
          <ThemedText>Loading...</ThemedText>
        </ThemedView>
      </SafeAreaView>
    );
  }

  if (!foodItem) {
    return (
      <SafeAreaView style={styles.container}>
        <ThemedView style={styles.errorContainer}>
          <ThemedText>Food item not found</ThemedText>
          <Button title="Go Back" onPress={() => router.back()} />
        </ThemedView>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <ThemedView style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <IconSymbol name="arrow.left" size={24} color={textColor} />
        </TouchableOpacity>
        <ThemedText style={styles.headerTitle}>{foodItem.name}</ThemedText>
        <View style={{ width: 24 }} />
      </ThemedView>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Food Image */}
        <Image
          source={{ uri: foodItem.image }}
          style={styles.foodImage}
          resizeMode="cover"
        />

        {/* Food Info */}
        <ThemedView style={styles.infoSection}>
          <View style={styles.titleRow}>
            <ThemedText style={styles.foodName}>{foodItem.name}</ThemedText>
            <View style={styles.badges}>
              {foodItem.isVegetarian && <ThemedText style={styles.badge}>🌱</ThemedText>}
              {foodItem.isSpicy && <ThemedText style={styles.badge}>🌶️</ThemedText>}
            </View>
          </View>

          <ThemedText style={styles.description}>{foodItem.description}</ThemedText>

          <View style={styles.metaRow}>
            <View style={styles.rating}>
              <IconSymbol name="star.fill" size={16} color="#FFD700" />
              <ThemedText style={styles.ratingText}>{foodItem.rating.toFixed(1)}</ThemedText>
            </View>
            <View style={styles.prepTime}>
              <IconSymbol name="clock" size={16} color={textColor} />
              <ThemedText style={styles.prepTimeText}>{foodItem.preparationTime}</ThemedText>
            </View>
          </View>

          <ThemedText style={styles.price}>${foodItem.price.toFixed(2)}</ThemedText>
        </ThemedView>

        {/* Quantity Selector */}
        <ThemedView style={styles.section}>
          <ThemedText style={styles.sectionTitle}>Quantity</ThemedText>
          <View style={styles.quantitySelector}>
            <TouchableOpacity
              style={styles.quantityButton}
              onPress={() => handleQuantityChange(-1)}
            >
              <IconSymbol name="minus" size={20} color={textColor} />
            </TouchableOpacity>
            <ThemedText style={styles.quantityText}>{quantity}</ThemedText>
            <TouchableOpacity
              style={styles.quantityButton}
              onPress={() => handleQuantityChange(1)}
            >
              <IconSymbol name="plus" size={20} color={textColor} />
            </TouchableOpacity>
          </View>
        </ThemedView>

        {/* Add-ons */}
        <ThemedView style={styles.section}>
          <ThemedText style={styles.sectionTitle}>Add-ons</ThemedText>
          {availableAddOns.map((addOn) => (
            <TouchableOpacity
              key={addOn.id}
              style={[
                styles.addOnItem,
                { backgroundColor },
                selectedAddOns.find(item => item.id === addOn.id) && styles.addOnSelected
              ]}
              onPress={() => toggleAddOn(addOn)}
            >
              <View style={styles.addOnInfo}>
                <ThemedText style={styles.addOnName}>{addOn.name}</ThemedText>
                <ThemedText style={styles.addOnPrice}>+${addOn.price.toFixed(2)}</ThemedText>
              </View>
              <View style={[
                styles.checkbox,
                selectedAddOns.find(item => item.id === addOn.id) && styles.checkboxSelected
              ]}>
                {selectedAddOns.find(item => item.id === addOn.id) && (
                  <IconSymbol name="checkmark" size={16} color="#ffffff" />
                )}
              </View>
            </TouchableOpacity>
          ))}
        </ThemedView>

        {/* Special Instructions */}
        <ThemedView style={styles.section}>
          <Input
            label="Special Instructions"
            placeholder="Any special requests..."
            value={specialInstructions}
            onChangeText={setSpecialInstructions}
            multiline
            numberOfLines={3}
          />
        </ThemedView>
      </ScrollView>

      {/* Footer */}
      <ThemedView style={styles.footer}>
        <View style={styles.totalContainer}>
          <ThemedText style={styles.totalLabel}>Total</ThemedText>
          <ThemedText style={styles.totalPrice}>${calculateTotal().toFixed(2)}</ThemedText>
        </View>
        <Button
          title={`Add ${quantity} to Cart`}
          onPress={handleAddToCart}
          style={styles.addToCartButton}
        />
      </ThemedView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
  },
  foodImage: {
    width: '100%',
    height: 250,
  },
  infoSection: {
    padding: 16,
  },
  titleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  foodName: {
    fontSize: 24,
    fontWeight: 'bold',
    flex: 1,
    marginRight: 16,
  },
  badges: {
    flexDirection: 'row',
  },
  badge: {
    fontSize: 20,
    marginLeft: 8,
  },
  description: {
    fontSize: 16,
    lineHeight: 24,
    opacity: 0.8,
    marginBottom: 16,
  },
  metaRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  rating: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 24,
  },
  ratingText: {
    fontSize: 16,
    marginLeft: 4,
    fontWeight: '500',
  },
  prepTime: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  prepTimeText: {
    fontSize: 16,
    marginLeft: 4,
    opacity: 0.8,
  },
  price: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#007AFF',
  },
  section: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  quantitySelector: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  quantityButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  quantityText: {
    fontSize: 20,
    fontWeight: 'bold',
    marginHorizontal: 32,
    minWidth: 40,
    textAlign: 'center',
  },
  addOnItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderRadius: 12,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  addOnSelected: {
    borderColor: '#007AFF',
    backgroundColor: '#f0f8ff',
  },
  addOnInfo: {
    flex: 1,
  },
  addOnName: {
    fontSize: 16,
    fontWeight: '500',
  },
  addOnPrice: {
    fontSize: 14,
    opacity: 0.7,
    marginTop: 2,
  },
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: '#cccccc',
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkboxSelected: {
    backgroundColor: '#007AFF',
    borderColor: '#007AFF',
  },
  footer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  totalContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  totalLabel: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  totalPrice: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#007AFF',
  },
  addToCartButton: {
    minHeight: 56,
  },
});
