import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView, TouchableOpacity, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useLocalSearchParams, router } from 'expo-router';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useThemeColor } from '@/hooks/useThemeColor';
import { useAuth } from '@/context';

export default function ReviewScreen() {
  const { orderId } = useLocalSearchParams<{ orderId: string }>();
  const { user } = useAuth();
  const [rating, setRating] = useState(0);
  const [comment, setComment] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const backgroundColor = useThemeColor({}, 'background');
  const textColor = useThemeColor({}, 'text');

  const handleStarPress = (selectedRating: number) => {
    setRating(selectedRating);
  };

  const handleSubmitReview = async () => {
    if (rating === 0) {
      Alert.alert('Rating Required', 'Please select a rating before submitting.');
      return;
    }

    if (comment.trim().length < 10) {
      Alert.alert('Comment Required', 'Please write at least 10 characters in your review.');
      return;
    }

    setIsSubmitting(true);

    // Simulate API call
    setTimeout(() => {
      setIsSubmitting(false);
      Alert.alert(
        'Review Submitted! 🌟',
        'Thank you for your feedback. Your review helps us improve our service.',
        [
          {
            text: 'View Orders',
            onPress: () => router.replace('/(tabs)/orders')
          },
          {
            text: 'Continue Shopping',
            onPress: () => router.replace('/(tabs)')
          }
        ]
      );
    }, 1500);
  };

  const renderStars = () => {
    const stars = [];
    for (let i = 1; i <= 5; i++) {
      stars.push(
        <TouchableOpacity
          key={i}
          onPress={() => handleStarPress(i)}
          style={styles.starButton}
        >
          <IconSymbol
            name={i <= rating ? "star.fill" : "star"}
            size={32}
            color={i <= rating ? "#FFD700" : "#CCCCCC"}
          />
        </TouchableOpacity>
      );
    }
    return stars;
  };

  const getRatingText = () => {
    switch (rating) {
      case 1: return "Poor";
      case 2: return "Fair";
      case 3: return "Good";
      case 4: return "Very Good";
      case 5: return "Excellent";
      default: return "Tap to rate";
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <ThemedView style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <IconSymbol name="arrow.left" size={24} color={textColor} />
        </TouchableOpacity>
        <ThemedText style={styles.headerTitle}>Write a Review</ThemedText>
        <View style={{ width: 24 }} />
      </ThemedView>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Order Info */}
        <ThemedView style={styles.orderInfo}>
          <ThemedText style={styles.orderTitle}>Order #{orderId}</ThemedText>
          <ThemedText style={styles.orderSubtitle}>
            How was your experience with this order?
          </ThemedText>
        </ThemedView>

        {/* Rating Section */}
        <ThemedView style={styles.ratingSection}>
          <ThemedText style={styles.sectionTitle}>Overall Rating</ThemedText>
          
          <View style={styles.starsContainer}>
            {renderStars()}
          </View>
          
          <ThemedText style={styles.ratingText}>
            {getRatingText()}
          </ThemedText>
        </ThemedView>

        {/* Comment Section */}
        <ThemedView style={styles.commentSection}>
          <Input
            label="Your Review"
            placeholder="Tell us about your experience... (minimum 10 characters)"
            value={comment}
            onChangeText={setComment}
            multiline
            numberOfLines={6}
            style={styles.commentInput}
          />
          
          <ThemedText style={styles.characterCount}>
            {comment.length}/500 characters
          </ThemedText>
        </ThemedView>

        {/* Review Guidelines */}
        <ThemedView style={[styles.guidelinesCard, { backgroundColor }]}>
          <ThemedText style={styles.guidelinesTitle}>Review Guidelines</ThemedText>
          
          <View style={styles.guidelineItem}>
            <IconSymbol name="checkmark" size={16} color="#4CAF50" />
            <ThemedText style={styles.guidelineText}>
              Be honest and helpful to other customers
            </ThemedText>
          </View>
          
          <View style={styles.guidelineItem}>
            <IconSymbol name="checkmark" size={16} color="#4CAF50" />
            <ThemedText style={styles.guidelineText}>
              Focus on food quality and delivery experience
            </ThemedText>
          </View>
          
          <View style={styles.guidelineItem}>
            <IconSymbol name="checkmark" size={16} color="#4CAF50" />
            <ThemedText style={styles.guidelineText}>
              Keep it respectful and constructive
            </ThemedText>
          </View>
        </ThemedView>

        {/* User Info */}
        <ThemedView style={styles.userInfo}>
          <ThemedText style={styles.userInfoText}>
            Reviewing as: {user?.name || 'Anonymous'}
          </ThemedText>
        </ThemedView>
      </ScrollView>

      {/* Submit Button */}
      <ThemedView style={styles.footer}>
        <Button
          title={isSubmitting ? "Submitting Review..." : "Submit Review"}
          onPress={handleSubmitReview}
          loading={isSubmitting}
          style={styles.submitButton}
        />
      </ThemedView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
  },
  orderInfo: {
    padding: 20,
    alignItems: 'center',
  },
  orderTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  orderSubtitle: {
    fontSize: 16,
    opacity: 0.7,
    textAlign: 'center',
  },
  ratingSection: {
    padding: 20,
    alignItems: 'center',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 20,
  },
  starsContainer: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  starButton: {
    padding: 4,
    marginHorizontal: 4,
  },
  ratingText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#007AFF',
  },
  commentSection: {
    padding: 20,
  },
  commentInput: {
    marginBottom: 8,
  },
  characterCount: {
    fontSize: 12,
    opacity: 0.6,
    textAlign: 'right',
  },
  guidelinesCard: {
    margin: 20,
    padding: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  guidelinesTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  guidelineItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  guidelineText: {
    fontSize: 14,
    marginLeft: 8,
    flex: 1,
  },
  userInfo: {
    padding: 20,
    alignItems: 'center',
  },
  userInfoText: {
    fontSize: 14,
    opacity: 0.6,
  },
  footer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  submitButton: {
    minHeight: 56,
  },
});
