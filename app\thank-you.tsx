import React, { useEffect } from 'react';
import { View, StyleSheet, Image } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router, useLocalSearchParams } from 'expo-router';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Button } from '@/components/ui/Button';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useThemeColor } from '@/hooks/useThemeColor';

export default function ThankYouScreen() {
  const { orderId } = useLocalSearchParams<{ orderId?: string }>();
  const backgroundColor = useThemeColor({}, 'background');
  const textColor = useThemeColor({}, 'text');

  useEffect(() => {
    // Auto redirect after 10 seconds
    const timer = setTimeout(() => {
      router.replace('/(tabs)');
    }, 10000);

    return () => clearTimeout(timer);
  }, []);

  const handleContinueShopping = () => {
    router.replace('/(tabs)');
  };

  const handleViewOrders = () => {
    router.replace('/(tabs)/orders');
  };

  const handleWriteReview = () => {
    if (orderId) {
      router.push(`/review/${orderId}`);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ThemedView style={styles.content}>
        {/* Success Animation/Icon */}
        <View style={styles.iconContainer}>
          <View style={[styles.successCircle, { backgroundColor: '#4CAF50' }]}>
            <IconSymbol name="checkmark" size={48} color="#ffffff" />
          </View>
        </View>

        {/* Thank You Message */}
        <View style={styles.messageContainer}>
          <ThemedText style={styles.title}>Thank You! 🎉</ThemedText>
          <ThemedText style={styles.subtitle}>
            Your order has been placed successfully
          </ThemedText>
          
          {orderId && (
            <View style={styles.orderInfo}>
              <ThemedText style={styles.orderLabel}>Order ID:</ThemedText>
              <ThemedText style={styles.orderId}>#{orderId}</ThemedText>
            </View>
          )}
        </View>

        {/* Order Details */}
        <ThemedView style={[styles.detailsCard, { backgroundColor }]}>
          <View style={styles.detailRow}>
            <IconSymbol name="clock" size={20} color={textColor} />
            <ThemedText style={styles.detailText}>
              Estimated delivery: 25-35 minutes
            </ThemedText>
          </View>
          
          <View style={styles.detailRow}>
            <IconSymbol name="location" size={20} color={textColor} />
            <ThemedText style={styles.detailText}>
              Delivering to your address
            </ThemedText>
          </View>
          
          <View style={styles.detailRow}>
            <IconSymbol name="envelope" size={20} color={textColor} />
            <ThemedText style={styles.detailText}>
              Confirmation sent to your email
            </ThemedText>
          </View>
        </ThemedView>

        {/* Action Buttons */}
        <View style={styles.buttonContainer}>
          <Button
            title="Continue Shopping"
            onPress={handleContinueShopping}
            style={styles.primaryButton}
          />
          
          <Button
            title="View Orders"
            onPress={handleViewOrders}
            variant="outline"
            style={styles.secondaryButton}
          />
          
          {orderId && (
            <Button
              title="Write a Review"
              onPress={handleWriteReview}
              variant="outline"
              style={styles.secondaryButton}
            />
          )}
        </View>

        {/* Auto Redirect Notice */}
        <ThemedText style={styles.autoRedirectText}>
          You'll be redirected to home in 10 seconds
        </ThemedText>
      </ThemedView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  iconContainer: {
    marginBottom: 32,
  },
  successCircle: {
    width: 120,
    height: 120,
    borderRadius: 60,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#4CAF50',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  messageContainer: {
    alignItems: 'center',
    marginBottom: 32,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 18,
    textAlign: 'center',
    opacity: 0.8,
    marginBottom: 16,
  },
  orderInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f0f8ff',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  orderLabel: {
    fontSize: 16,
    fontWeight: '500',
    marginRight: 8,
  },
  orderId: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#007AFF',
  },
  detailsCard: {
    width: '100%',
    padding: 20,
    borderRadius: 16,
    marginBottom: 32,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  detailText: {
    fontSize: 16,
    marginLeft: 12,
    flex: 1,
  },
  buttonContainer: {
    width: '100%',
    gap: 12,
  },
  primaryButton: {
    minHeight: 56,
  },
  secondaryButton: {
    minHeight: 48,
  },
  autoRedirectText: {
    fontSize: 14,
    opacity: 0.6,
    textAlign: 'center',
    marginTop: 24,
  },
});
