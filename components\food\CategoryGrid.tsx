import React from 'react';
import { View, StyleSheet, TouchableOpacity, Image } from 'react-native';
import { FlatGrid } from 'react-native-super-grid';
import { ThemedText } from '@/components/ThemedText';
import { Category } from '@/types';

interface CategoryGridProps {
  categories: Category[];
  onCategoryPress: (category: Category) => void;
  numColumns?: number;
}

export function CategoryGrid({ 
  categories, 
  onCategoryPress, 
  numColumns = 3 
}: CategoryGridProps) {
  const renderCategory = ({ item }: { item: Category }) => (
    <TouchableOpacity
      style={styles.categoryItem}
      onPress={() => onCategoryPress(item)}
      activeOpacity={0.8}
    >
      <View style={[styles.categoryImageContainer, { backgroundColor: item.color }]}>
        <Image
          source={{ uri: item.image }}
          style={styles.categoryImage}
          resizeMode="cover"
        />
      </View>
      <ThemedText style={styles.categoryName} numberOfLines={1}>
        {item.name}
      </ThemedText>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <ThemedText style={styles.sectionTitle}>Categories</ThemedText>
      <FlatGrid
        itemDimension={80}
        data={categories}
        style={styles.grid}
        spacing={16}
        renderItem={renderCategory}
        staticDimension={undefined}
        fixed={false}
        maxItemsPerRow={numColumns}
        scrollEnabled={false}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
    paddingHorizontal: 16,
  },
  grid: {
    paddingHorizontal: 16,
  },
  categoryItem: {
    alignItems: 'center',
  },
  categoryImageContainer: {
    width: 80,
    height: 80,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  categoryImage: {
    width: 50,
    height: 50,
    borderRadius: 8,
  },
  categoryName: {
    fontSize: 14,
    fontWeight: '500',
    textAlign: 'center',
  },
});
