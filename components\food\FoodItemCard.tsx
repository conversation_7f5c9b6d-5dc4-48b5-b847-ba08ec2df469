import { router } from 'expo-router';
import React from 'react';
import { Alert, Image, StyleSheet, TouchableOpacity, View } from 'react-native';

import { ThemedText } from '@/components/ThemedText';
import { Button } from '@/components/ui/Button';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useCart } from '@/context';
import { useThemeColor } from '@/hooks/useThemeColor';
import { FoodItem } from '@/types';

interface FoodItemCardProps {
  foodItem: FoodItem;
  onPress?: (foodItem: FoodItem) => void;
}

export function FoodItemCard({ foodItem, onPress }: FoodItemCardProps) {
  const { addToCart, getItemQuantity } = useCart();
  const backgroundColor = useThemeColor({}, 'background');
  const textColor = useThemeColor({}, 'text');
  
  const quantity = getItemQuantity(foodItem.id);

  const handleAddToCart = () => {
    addToCart(foodItem, 1);
    Alert.alert('Added to Cart', `${foodItem.name} has been added to your cart!`);
  };

  const handlePress = () => {
    if (onPress) {
      onPress(foodItem);
    } else {
      // Navigate to product detail screen
      router.push(`/product/${foodItem.id}`);
    }
  };

  return (
    <TouchableOpacity
      style={[styles.container, { backgroundColor }]}
      onPress={handlePress}
      activeOpacity={0.8}
    >
      <View style={styles.content}>
        <View style={styles.imageContainer}>
          <Image
            source={{ uri: foodItem.image }}
            style={styles.image}
            resizeMode="cover"
          />
          {quantity > 0 && (
            <View style={styles.quantityBadge}>
              <ThemedText style={styles.quantityText}>{quantity}</ThemedText>
            </View>
          )}
        </View>

        <View style={styles.info}>
          <View style={styles.header}>
            <ThemedText style={styles.name} numberOfLines={1}>
              {foodItem.name}
            </ThemedText>
            <View style={styles.badges}>
              {foodItem.isVegetarian && (
                <View style={styles.vegBadge}>
                  <ThemedText style={styles.vegText}>🌱</ThemedText>
                </View>
              )}
              {foodItem.isSpicy && (
                <View style={styles.spicyBadge}>
                  <ThemedText style={styles.spicyText}>🌶️</ThemedText>
                </View>
              )}
            </View>
          </View>

          <ThemedText style={styles.description} numberOfLines={2}>
            {foodItem.description}
          </ThemedText>

          <View style={styles.meta}>
            <View style={styles.rating}>
              <IconSymbol name="star.fill" size={14} color="#FFD700" />
              <ThemedText style={styles.ratingText}>
                {foodItem.rating.toFixed(1)}
              </ThemedText>
            </View>
            
            <View style={styles.prepTime}>
              <IconSymbol name="clock" size={14} color={textColor} />
              <ThemedText style={styles.prepTimeText}>
                {foodItem.preparationTime}
              </ThemedText>
            </View>
          </View>

          <View style={styles.footer}>
            <ThemedText style={styles.price}>
              ${foodItem.price.toFixed(2)}
            </ThemedText>
            
            <Button
              title="Add"
              onPress={handleAddToCart}
              size="small"
              style={styles.addButton}
            />
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 16,
    marginHorizontal: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
    overflow: 'hidden',
  },
  content: {
    flexDirection: 'row',
    padding: 16,
  },
  imageContainer: {
    position: 'relative',
    marginRight: 16,
  },
  image: {
    width: 100,
    height: 100,
    borderRadius: 12,
  },
  quantityBadge: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: '#007AFF',
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  quantityText: {
    color: '#ffffff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  info: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 4,
  },
  name: {
    fontSize: 18,
    fontWeight: 'bold',
    flex: 1,
    marginRight: 8,
  },
  badges: {
    flexDirection: 'row',
  },
  vegBadge: {
    marginLeft: 4,
  },
  vegText: {
    fontSize: 16,
  },
  spicyBadge: {
    marginLeft: 4,
  },
  spicyText: {
    fontSize: 16,
  },
  description: {
    fontSize: 14,
    opacity: 0.7,
    marginBottom: 8,
    lineHeight: 20,
  },
  meta: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  rating: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
  },
  ratingText: {
    fontSize: 14,
    marginLeft: 4,
    fontWeight: '500',
  },
  prepTime: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  prepTimeText: {
    fontSize: 14,
    marginLeft: 4,
    opacity: 0.8,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  price: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#007AFF',
  },
  addButton: {
    minWidth: 80,
  },
});
