import React from 'react';
import { View, StyleSheet, TouchableOpacity, Image } from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useThemeColor } from '@/hooks/useThemeColor';
import { Restaurant } from '@/types';

interface RestaurantCardProps {
  restaurant: Restaurant;
  onPress: (restaurant: Restaurant) => void;
}

export function RestaurantCard({ restaurant, onPress }: RestaurantCardProps) {
  const backgroundColor = useThemeColor({}, 'background');
  const textColor = useThemeColor({}, 'text');

  return (
    <TouchableOpacity
      style={[styles.container, { backgroundColor }]}
      onPress={() => onPress(restaurant)}
      activeOpacity={0.8}
    >
      <Image
        source={{ uri: restaurant.image }}
        style={styles.image}
        resizeMode="cover"
      />
      
      <View style={styles.content}>
        <View style={styles.header}>
          <ThemedText style={styles.name} numberOfLines={1}>
            {restaurant.name}
          </ThemedText>
          <View style={styles.ratingContainer}>
            <IconSymbol name="star.fill" size={14} color="#FFD700" />
            <ThemedText style={styles.rating}>
              {restaurant.rating.toFixed(1)}
            </ThemedText>
          </View>
        </View>
        
        <ThemedText style={styles.cuisine} numberOfLines={1}>
          {restaurant.cuisine.join(' • ')}
        </ThemedText>
        
        <View style={styles.footer}>
          <View style={styles.deliveryInfo}>
            <IconSymbol name="clock" size={14} color={textColor} />
            <ThemedText style={styles.deliveryTime}>
              {restaurant.deliveryTime}
            </ThemedText>
          </View>
          
          <View style={styles.deliveryInfo}>
            <IconSymbol name="location" size={14} color={textColor} />
            <ThemedText style={styles.distance}>
              {restaurant.distance}
            </ThemedText>
          </View>
          
          <ThemedText style={styles.deliveryFee}>
            ${restaurant.deliveryFee.toFixed(2)} delivery
          </ThemedText>
        </View>
        
        {!restaurant.isOpen && (
          <View style={styles.closedOverlay}>
            <ThemedText style={styles.closedText}>Closed</ThemedText>
          </View>
        )}
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 16,
    marginHorizontal: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
    overflow: 'hidden',
  },
  image: {
    width: '100%',
    height: 160,
  },
  content: {
    padding: 16,
    position: 'relative',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  name: {
    fontSize: 18,
    fontWeight: 'bold',
    flex: 1,
    marginRight: 8,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rating: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 4,
  },
  cuisine: {
    fontSize: 14,
    opacity: 0.7,
    marginBottom: 12,
  },
  footer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  deliveryInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  deliveryTime: {
    fontSize: 12,
    marginLeft: 4,
    opacity: 0.8,
  },
  distance: {
    fontSize: 12,
    marginLeft: 4,
    opacity: 0.8,
  },
  deliveryFee: {
    fontSize: 12,
    fontWeight: '600',
    opacity: 0.8,
  },
  closedOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 16,
  },
  closedText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});
