import React, { useState } from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { CartCounter } from '@/components/ui/CartCounter';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useThemeColor } from '@/hooks/useThemeColor';
import { router } from 'expo-router';
import { DrawerMenu } from './DrawerMenu';

interface AppHeaderProps {
  title: string;
  showBackButton?: boolean;
  showCartButton?: boolean;
  showMenuButton?: boolean;
  onBackPress?: () => void;
  subtitle?: string;
}

export function AppHeader({
  title,
  showBackButton = false,
  showCartButton = true,
  showMenuButton = true,
  onBackPress,
  subtitle
}: AppHeaderProps) {
  const [isDrawerVisible, setIsDrawerVisible] = useState(false);
  const textColor = useThemeColor({}, 'text');
  const backgroundColor = useThemeColor({}, 'background');
  const tintColor = useThemeColor({}, 'tint');

  const handleBackPress = () => {
    if (onBackPress) {
      onBackPress();
    } else {
      router.back();
    }
  };

  const handleCartPress = () => {
    router.push('/(tabs)/cart');
  };

  const handleMenuPress = () => {
    setIsDrawerVisible(true);
  };

  const closeDrawer = () => {
    setIsDrawerVisible(false);
  };

  return (
    <>
      <SafeAreaView style={[styles.container, { backgroundColor }]}>
        <ThemedView style={[styles.header, {
          shadowColor: textColor,
          borderBottomColor: textColor + '20'
        }]}>
          {/* Left Side */}
          <View style={styles.leftSection}>
            {showBackButton ? (
              <TouchableOpacity
                style={[styles.iconButton, { backgroundColor: tintColor + '15' }]}
                onPress={handleBackPress}
                activeOpacity={0.7}
              >
                <IconSymbol name="arrow.left" size={24} color={tintColor} />
              </TouchableOpacity>
            ) : showMenuButton ? (
              <TouchableOpacity
                style={[styles.iconButton, { backgroundColor: tintColor + '15' }]}
                onPress={handleMenuPress}
                activeOpacity={0.7}
              >
                <IconSymbol name="line.horizontal.3" size={24} color={tintColor} />
              </TouchableOpacity>
            ) : (
              <View style={styles.iconButton} />
            )}
          </View>

          {/* Center */}
          <View style={styles.centerSection}>
            <ThemedText style={styles.title} numberOfLines={1}>
              {title}
            </ThemedText>
            {subtitle && (
              <ThemedText style={styles.subtitle} numberOfLines={1}>
                {subtitle}
              </ThemedText>
            )}
          </View>

          {/* Right Side */}
          <View style={styles.rightSection}>
            {showCartButton && (
              <TouchableOpacity
                style={[styles.iconButton, { backgroundColor: tintColor + '15' }]}
                onPress={handleCartPress}
                activeOpacity={0.7}
              >
                <IconSymbol name="cart" size={24} color={tintColor} />
                <CartCounter size="small" />
              </TouchableOpacity>
            )}
          </View>
        </ThemedView>
      </SafeAreaView>

      {/* Drawer Menu */}
      <DrawerMenu visible={isDrawerVisible} onClose={closeDrawer} />
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
    zIndex: 1000,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    minHeight: 60,
    borderBottomWidth: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  leftSection: {
    width: 50,
    alignItems: 'flex-start',
  },
  centerSection: {
    flex: 1,
    alignItems: 'center',
    paddingHorizontal: 16,
  },
  rightSection: {
    width: 50,
    alignItems: 'flex-end',
  },
  iconButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 14,
    opacity: 0.7,
    textAlign: 'center',
    marginTop: 2,
  },
});
