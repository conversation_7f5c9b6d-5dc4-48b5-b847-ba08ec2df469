import { router } from 'expo-router';
import React from 'react';
import { Dimensions, Modal, StyleSheet, TouchableOpacity, View } from 'react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming
} from 'react-native-reanimated';
import { SafeAreaView } from 'react-native-safe-area-context';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { CartCounter } from '@/components/ui/CartCounter';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useAuth, useCart } from '@/context';
import { useThemeColor } from '@/hooks/useThemeColor';

interface DrawerMenuProps {
  visible: boolean;
  onClose: () => void;
}

const { width: screenWidth } = Dimensions.get('window');

export function DrawerMenu({ visible, onClose }: DrawerMenuProps) {
  const { user, logout, checkAuthStatus, isAuthenticated } = useAuth();
  const { items } = useCart();
  const backgroundColor = useThemeColor({}, 'background');
  const textColor = useThemeColor({}, 'text');

  const translateX = useSharedValue(-screenWidth);

  React.useEffect(() => {
    if (visible) {
      translateX.value = withTiming(0, { duration: 300 });
    } else {
      translateX.value = withTiming(-screenWidth, { duration: 300 });
    }
  }, [visible]);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateX: translateX.value }],
  }));

  const menuItems = [
    {
      id: 'home',
      title: 'Home',
      icon: 'house.fill',
      route: '/(tabs)',
      showBadge: false,
    },
    {
      id: 'menu',
      title: 'Menu',
      icon: 'list.bullet',
      route: '/(tabs)/menu',
      showBadge: false,
    },
    {
      id: 'cart',
      title: 'Cart',
      icon: 'cart',
      route: '/(tabs)/cart',
      showBadge: true,
    },
    {
      id: 'orders',
      title: 'Order History',
      icon: 'bag',
      route: '/(tabs)/orders',
      showBadge: false,
    },
    {
      id: 'profile',
      title: 'Profile',
      icon: 'person',
      route: '/(tabs)/profile',
      showBadge: false,
    },
  ];

  const handleMenuItemPress = (route: string) => {
    onClose();
    setTimeout(() => {
      router.push(route as any);
    }, 300);
  };

  const handleLogout = async () => {
    onClose();
    setTimeout(async () => {
      try {
        await logout();
        await checkAuthStatus(); // Ensure state is refreshed
        setTimeout(() => {
          router.replace('/(auth)/login');
        }, 100); // Small delay to allow state update
      } catch (error) {
        console.error('Logout error:', error);
        // Force logout even if API fails
        router.replace('/(auth)/login');
      }
    }, 300);
  };

  const totalItems = items.reduce((sum, item) => sum + item.quantity, 0);

  return (
    <Modal
      visible={visible}
      transparent
      animationType="none"
      onRequestClose={onClose}
    >
      {/* Backdrop */}
      <TouchableOpacity
        style={styles.backdrop}
        activeOpacity={1}
        onPress={onClose}
      >
        {/* Drawer */}
        <Animated.View style={[animatedStyle, { width: screenWidth * 0.8 }]}>
          <TouchableOpacity activeOpacity={1} style={{ flex: 1 }}>
            <ThemedView style={[styles.drawer, { backgroundColor }]}>
              <SafeAreaView style={styles.drawerContent}>
                {/* Header */}
                <View style={styles.header}>
                  <View style={styles.userInfo}>
                    <View style={styles.avatar}>
                      <ThemedText style={styles.avatarText}>
                        {user?.name?.charAt(0).toUpperCase() || 'U'}
                      </ThemedText>
                    </View>
                    <View style={styles.userDetails}>
                      <ThemedText style={styles.userName}>
                        {user?.name || 'Guest User'}
                      </ThemedText>
                      <ThemedText style={styles.userEmail}>
                        {user?.email || '<EMAIL>'}
                      </ThemedText>
                    </View>
                  </View>
                </View>

                {/* Menu Items */}
                <View style={styles.menuItems}>
                  {menuItems.map((item) => (
                    <TouchableOpacity
                      key={item.id}
                      style={styles.menuItem}
                      onPress={() => handleMenuItemPress(item.route)}
                    >
                      <View style={styles.menuItemLeft}>
                        <View style={styles.iconContainer}>
                          <IconSymbol
                            name={item.icon as any}
                            size={24}
                            color={textColor}
                          />
                          {item.showBadge && totalItems > 0 && (
                            <CartCounter size="small" />
                          )}
                        </View>
                        <ThemedText style={styles.menuItemText}>
                          {item.title}
                        </ThemedText>
                      </View>
                      <IconSymbol
                        name="chevron.right"
                        size={16}
                        color={textColor}
                      />
                    </TouchableOpacity>
                  ))}
                </View>

                {/* Divider */}
                <View style={styles.divider} />

                {/* App Info */}
                <View style={styles.appInfo}>
                  <ThemedText style={styles.appName}>Delicious Kitchen</ThemedText>
                  <ThemedText style={styles.appVersion}>Version 1.0.0</ThemedText>
                </View>

                {/* Logout Button */}
                <TouchableOpacity
                  style={styles.logoutButton}
                  onPress={handleLogout}
                >
                  <IconSymbol name="arrow.left" size={20} color="#FF4444" />
                  <ThemedText style={styles.logoutText}>Logout</ThemedText>
                </TouchableOpacity>
              </SafeAreaView>
            </ThemedView>
          </TouchableOpacity>
        </Animated.View>
      </TouchableOpacity>
    </Modal>
  );
}

const styles = StyleSheet.create({
  backdrop: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    flexDirection: 'row',
  },
  drawer: {
    flex: 1,
    shadowColor: '#000',
    shadowOffset: { width: 2, height: 0 },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 16,
  },
  drawerContent: {
    flex: 1,
  },
  header: {
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#007AFF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  avatarText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#ffffff',
  },
  userDetails: {
    flex: 1,
  },
  userName: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  userEmail: {
    fontSize: 14,
    opacity: 0.7,
  },
  menuItems: {
    flex: 1,
    paddingTop: 20,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  menuItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    position: 'relative',
    marginRight: 16,
  },
  menuItemText: {
    fontSize: 16,
    fontWeight: '500',
  },
  divider: {
    height: 1,
    backgroundColor: '#e0e0e0',
    marginHorizontal: 20,
    marginVertical: 16,
  },
  appInfo: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    alignItems: 'center',
  },
  appName: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  appVersion: {
    fontSize: 12,
    opacity: 0.6,
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    marginBottom: 20,
  },
  logoutText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#FF4444',
    marginLeft: 16,
  },
});
