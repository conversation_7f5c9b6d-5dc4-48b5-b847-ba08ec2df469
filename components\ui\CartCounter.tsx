import React from 'react';
import { View, StyleSheet } from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { useCart } from '@/context';

interface CartCounterProps {
  size?: 'small' | 'medium' | 'large';
  color?: string;
}

export function CartCounter({ size = 'medium', color = '#FF4444' }: CartCounterProps) {
  const { items } = useCart();
  
  const totalItems = items.reduce((sum, item) => sum + item.quantity, 0);
  
  if (totalItems === 0) return null;

  const getSize = () => {
    switch (size) {
      case 'small':
        return { width: 16, height: 16, fontSize: 10 };
      case 'medium':
        return { width: 20, height: 20, fontSize: 12 };
      case 'large':
        return { width: 24, height: 24, fontSize: 14 };
      default:
        return { width: 20, height: 20, fontSize: 12 };
    }
  };

  const sizeStyle = getSize();

  return (
    <View style={[
      styles.container,
      {
        width: sizeStyle.width,
        height: sizeStyle.height,
        backgroundColor: color,
        borderRadius: sizeStyle.width / 2,
      }
    ]}>
      <ThemedText style={[
        styles.text,
        { fontSize: sizeStyle.fontSize }
      ]}>
        {totalItems > 99 ? '99+' : totalItems}
      </ThemedText>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
    top: -8,
    right: -8,
    zIndex: 1,
  },
  text: {
    color: '#ffffff',
    fontWeight: 'bold',
    textAlign: 'center',
  },
});
