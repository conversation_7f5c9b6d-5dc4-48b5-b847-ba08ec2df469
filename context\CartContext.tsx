import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { CartItem, CartState, FoodItem, AddOn } from '@/types';
import { storageService } from '@/services/storage';

interface CartContextType extends CartState {
  addToCart: (foodItem: FoodItem, quantity?: number, specialInstructions?: string, addOns?: AddOn[]) => void;
  removeFromCart: (itemId: string) => void;
  updateQuantity: (itemId: string, quantity: number) => void;
  clearCart: () => void;
  getItemQuantity: (foodItemId: string) => number;
  loadCart: () => Promise<void>;
}

const CartContext = createContext<CartContextType | undefined>(undefined);

type CartAction =
  | { type: 'SET_CART'; payload: CartItem[] }
  | { type: 'ADD_ITEM'; payload: CartItem }
  | { type: 'REMOVE_ITEM'; payload: string }
  | { type: 'UPDATE_QUANTITY'; payload: { itemId: string; quantity: number } }
  | { type: 'CLEAR_CART' };

const calculateTotals = (items: CartItem[]) => {
  const subtotal = items.reduce((sum, item) => {
    const addOnsTotal = item.addOns?.reduce((addOnSum, addOn) => addOnSum + addOn.price, 0) || 0;
    return sum + (item.foodItem.price + addOnsTotal) * item.quantity;
  }, 0);

  const deliveryFee = subtotal > 25 ? 0 : 2.99; // Free delivery over $25
  const tax = subtotal * 0.08; // 8% tax
  const grandTotal = subtotal + deliveryFee + tax;

  return {
    total: subtotal,
    deliveryFee,
    tax,
    grandTotal
  };
};

const cartReducer = (state: CartState, action: CartAction): CartState => {
  let newItems: CartItem[];
  
  switch (action.type) {
    case 'SET_CART':
      const totals = calculateTotals(action.payload);
      return {
        items: action.payload,
        ...totals
      };

    case 'ADD_ITEM':
      // Check if item already exists (same food item and add-ons)
      const existingItemIndex = state.items.findIndex(item => 
        item.foodItem.id === action.payload.foodItem.id &&
        JSON.stringify(item.addOns) === JSON.stringify(action.payload.addOns)
      );

      if (existingItemIndex >= 0) {
        // Update quantity of existing item
        newItems = state.items.map((item, index) =>
          index === existingItemIndex
            ? { ...item, quantity: item.quantity + action.payload.quantity }
            : item
        );
      } else {
        // Add new item
        newItems = [...state.items, action.payload];
      }

      const addTotals = calculateTotals(newItems);
      return {
        items: newItems,
        ...addTotals
      };

    case 'REMOVE_ITEM':
      newItems = state.items.filter(item => item.id !== action.payload);
      const removeTotals = calculateTotals(newItems);
      return {
        items: newItems,
        ...removeTotals
      };

    case 'UPDATE_QUANTITY':
      if (action.payload.quantity <= 0) {
        // Remove item if quantity is 0 or less
        newItems = state.items.filter(item => item.id !== action.payload.itemId);
      } else {
        newItems = state.items.map(item =>
          item.id === action.payload.itemId
            ? { ...item, quantity: action.payload.quantity }
            : item
        );
      }
      
      const updateTotals = calculateTotals(newItems);
      return {
        items: newItems,
        ...updateTotals
      };

    case 'CLEAR_CART':
      return {
        items: [],
        total: 0,
        deliveryFee: 0,
        tax: 0,
        grandTotal: 0
      };

    default:
      return state;
  }
};

const initialState: CartState = {
  items: [],
  total: 0,
  deliveryFee: 0,
  tax: 0,
  grandTotal: 0
};

interface CartProviderProps {
  children: ReactNode;
}

export const CartProvider: React.FC<CartProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(cartReducer, initialState);

  const loadCart = async () => {
    try {
      const savedCart = await storageService.getCart();
      dispatch({ type: 'SET_CART', payload: savedCart });
    } catch (error) {
      console.error('Error loading cart:', error);
    }
  };

  const saveCart = async (items: CartItem[]) => {
    try {
      await storageService.saveCart(items);
    } catch (error) {
      console.error('Error saving cart:', error);
    }
  };

  const addToCart = (
    foodItem: FoodItem, 
    quantity: number = 1, 
    specialInstructions?: string, 
    addOns?: AddOn[]
  ) => {
    const cartItem: CartItem = {
      id: `${foodItem.id}_${Date.now()}_${Math.random()}`,
      foodItem,
      quantity,
      specialInstructions,
      addOns
    };

    dispatch({ type: 'ADD_ITEM', payload: cartItem });
  };

  const removeFromCart = (itemId: string) => {
    dispatch({ type: 'REMOVE_ITEM', payload: itemId });
  };

  const updateQuantity = (itemId: string, quantity: number) => {
    dispatch({ type: 'UPDATE_QUANTITY', payload: { itemId, quantity } });
  };

  const clearCart = () => {
    dispatch({ type: 'CLEAR_CART' });
  };

  const getItemQuantity = (foodItemId: string): number => {
    return state.items
      .filter(item => item.foodItem.id === foodItemId)
      .reduce((total, item) => total + item.quantity, 0);
  };

  // Save cart to storage whenever it changes
  useEffect(() => {
    saveCart(state.items);
  }, [state.items]);

  // Load cart on mount
  useEffect(() => {
    loadCart();
  }, []);

  const value: CartContextType = {
    ...state,
    addToCart,
    removeFromCart,
    updateQuantity,
    clearCart,
    getItemQuantity,
    loadCart
  };

  return (
    <CartContext.Provider value={value}>
      {children}
    </CartContext.Provider>
  );
};

export const useCart = (): CartContextType => {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
};
