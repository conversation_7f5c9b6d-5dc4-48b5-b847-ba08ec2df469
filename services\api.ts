import {
  ApiResponse,
  Banner,
  Category,
  FoodItem,
  LoginForm,
  Order,
  RegisterForm,
  Restaurant,
  User
} from '@/types';

// Single Restaurant Data
const RESTAURANT_INFO: Restaurant = {
  id: '1',
  name: 'Delicious Kitchen',
  image: 'https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?w=800',
  rating: 4.7,
  deliveryTime: '20-30 min',
  deliveryFee: 2.99,
  cuisine: ['Multi-Cuisine', 'Fresh & Healthy'],
  isOpen: true,
  distance: '1.5 km'
};

const DUMMY_CATEGORIES: Category[] = [
  { id: '1', name: 'Appetizers', image: 'https://images.unsplash.com/photo-1541014741259-de529411b96a?w=200', color: '#FF6B6B' },
  { id: '2', name: 'Main Course', image: 'https://images.unsplash.com/photo-**********-b9f581a1996d?w=200', color: '#4ECDC4' },
  { id: '3', name: 'Desser<PERSON>', image: 'https://images.unsplash.com/photo-**********-0bccd828d307?w=200', color: '#45B7D1' },
  { id: '4', name: 'Beverages', image: 'https://images.unsplash.com/photo-**********-f90425340c7e?w=200', color: '#96CEB4' },
  { id: '5', name: 'Salads', image: 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?w=200', color: '#FFEAA7' },
  { id: '6', name: 'Soups', image: 'https://images.unsplash.com/photo-**********-23ac45744acd?w=200', color: '#FD79A8' }
];

const DUMMY_BANNERS: Banner[] = [
  {
    id: '1',
    title: 'Free Delivery',
    subtitle: 'On orders over $25',
    image: 'https://images.unsplash.com/photo-1504674900247-0877df9cc836?w=800',
    backgroundColor: '#FF6B6B'
  },
  {
    id: '2',
    title: '50% Off',
    subtitle: 'First order discount',
    image: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=800',
    backgroundColor: '#4ECDC4'
  },
  {
    id: '3',
    title: 'New Restaurant',
    subtitle: 'Sushi Zen now available',
    image: 'https://images.unsplash.com/photo-1579584425555-c3ce17fd4351?w=800',
    backgroundColor: '#45B7D1'
  }
];

const DUMMY_FOOD_ITEMS: FoodItem[] = [
  // Appetizers
  {
    id: '1',
    name: 'Crispy Chicken Wings',
    description: 'Golden crispy wings served with buffalo sauce and ranch dip',
    price: 12.99,
    image: 'https://images.unsplash.com/photo-1527477396000-e27163b481c2?w=400',
    category: 'Appetizers',
    restaurant: RESTAURANT_INFO,
    rating: 4.6,
    isVegetarian: false,
    isSpicy: true,
    preparationTime: '15-20 min',
    ingredients: ['Chicken wings', 'Buffalo sauce', 'Ranch dip', 'Celery']
  },
  {
    id: '2',
    name: 'Mozzarella Sticks',
    description: 'Crispy breaded mozzarella sticks with marinara sauce',
    price: 8.99,
    image: 'https://images.unsplash.com/photo-1541592106381-b31e9677c0e5?w=400',
    category: 'Appetizers',
    restaurant: RESTAURANT_INFO,
    rating: 4.4,
    isVegetarian: true,
    isSpicy: false,
    preparationTime: '10-15 min',
    ingredients: ['Mozzarella cheese', 'Breadcrumbs', 'Marinara sauce']
  },
  // Main Course
  {
    id: '3',
    name: 'Grilled Salmon',
    description: 'Fresh Atlantic salmon grilled to perfection with lemon herbs',
    price: 24.99,
    image: 'https://images.unsplash.com/photo-1467003909585-2f8a72700288?w=400',
    category: 'Main Course',
    restaurant: RESTAURANT_INFO,
    rating: 4.8,
    isVegetarian: false,
    isSpicy: false,
    preparationTime: '20-25 min',
    ingredients: ['Atlantic salmon', 'Lemon', 'Herbs', 'Olive oil']
  },
  {
    id: '4',
    name: 'Chicken Alfredo Pasta',
    description: 'Creamy alfredo pasta with grilled chicken and parmesan',
    price: 18.99,
    image: 'https://images.unsplash.com/photo-1621996346565-e3dbc353d2e5?w=400',
    category: 'Main Course',
    restaurant: RESTAURANT_INFO,
    rating: 4.7,
    isVegetarian: false,
    isSpicy: false,
    preparationTime: '18-22 min',
    ingredients: ['Fettuccine pasta', 'Grilled chicken', 'Alfredo sauce', 'Parmesan']
  },
  {
    id: '5',
    name: 'Vegetarian Pizza',
    description: 'Fresh vegetables on crispy crust with mozzarella cheese',
    price: 16.99,
    image: 'https://images.unsplash.com/photo-1513104890138-7c749659a591?w=400',
    category: 'Main Course',
    restaurant: RESTAURANT_INFO,
    rating: 4.5,
    isVegetarian: true,
    isSpicy: false,
    preparationTime: '15-20 min',
    ingredients: ['Pizza dough', 'Mozzarella', 'Bell peppers', 'Mushrooms', 'Olives']
  },
  // Desserts
  {
    id: '6',
    name: 'Chocolate Lava Cake',
    description: 'Warm chocolate cake with molten center, served with vanilla ice cream',
    price: 7.99,
    image: 'https://images.unsplash.com/photo-1606313564200-e75d5e30476c?w=400',
    category: 'Desserts',
    restaurant: RESTAURANT_INFO,
    rating: 4.9,
    isVegetarian: true,
    isSpicy: false,
    preparationTime: '12-15 min',
    ingredients: ['Dark chocolate', 'Butter', 'Eggs', 'Vanilla ice cream']
  },
  {
    id: '7',
    name: 'Cheesecake',
    description: 'Classic New York style cheesecake with berry compote',
    price: 6.99,
    image: 'https://images.unsplash.com/photo-1533134242443-d4fd215305ad?w=400',
    category: 'Desserts',
    restaurant: RESTAURANT_INFO,
    rating: 4.6,
    isVegetarian: true,
    isSpicy: false,
    preparationTime: '5-8 min',
    ingredients: ['Cream cheese', 'Graham crackers', 'Mixed berries', 'Sugar']
  },
  // Beverages
  {
    id: '8',
    name: 'Fresh Orange Juice',
    description: 'Freshly squeezed orange juice, no added sugar',
    price: 4.99,
    image: 'https://images.unsplash.com/photo-**********-f90425340c7e?w=400',
    category: 'Beverages',
    restaurant: RESTAURANT_INFO,
    rating: 4.3,
    isVegetarian: true,
    isSpicy: false,
    preparationTime: '2-3 min',
    ingredients: ['Fresh oranges']
  },
  {
    id: '9',
    name: 'Iced Coffee',
    description: 'Cold brew coffee served over ice with milk',
    price: 3.99,
    image: 'https://images.unsplash.com/photo-1461023058943-07fcbe16d735?w=400',
    category: 'Beverages',
    restaurant: RESTAURANT_INFO,
    rating: 4.4,
    isVegetarian: true,
    isSpicy: false,
    preparationTime: '3-5 min',
    ingredients: ['Coffee beans', 'Ice', 'Milk']
  },
  // Salads
  {
    id: '10',
    name: 'Caesar Salad',
    description: 'Crisp romaine lettuce with caesar dressing, croutons and parmesan',
    price: 11.99,
    image: 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?w=400',
    category: 'Salads',
    restaurant: RESTAURANT_INFO,
    rating: 4.5,
    isVegetarian: true,
    isSpicy: false,
    preparationTime: '8-10 min',
    ingredients: ['Romaine lettuce', 'Caesar dressing', 'Croutons', 'Parmesan']
  },
  // Soups
  {
    id: '11',
    name: 'Tomato Basil Soup',
    description: 'Creamy tomato soup with fresh basil and herbs',
    price: 6.99,
    image: 'https://images.unsplash.com/photo-**********-23ac45744acd?w=400',
    category: 'Soups',
    restaurant: RESTAURANT_INFO,
    rating: 4.4,
    isVegetarian: true,
    isSpicy: false,
    preparationTime: '10-12 min',
    ingredients: ['Fresh tomatoes', 'Basil', 'Cream', 'Herbs']
  }
];

// Simulate API delay
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// API Functions
export const authAPI = {
  login: async (credentials: LoginForm): Promise<ApiResponse<User>> => {
    await delay(1000);

    // Simulate login validation
    if (credentials.email === '<EMAIL>' && credentials.password === 'password') {
      return {
        success: true,
        data: {
          id: '1',
          name: 'John Doe',
          email: credentials.email,
          phone: '+1234567890'
        }
      };
    }

    return {
      success: false,
      data: null as any,
      error: 'Invalid credentials'
    };
  },

  register: async (userData: RegisterForm): Promise<ApiResponse<User>> => {
    await delay(1000);

    return {
      success: true,
      data: {
        id: Date.now().toString(),
        name: userData.name,
        email: userData.email,
        phone: userData.phone
      }
    };
  },

  logout: async (): Promise<ApiResponse<null>> => {
    await delay(500);
    return { success: true, data: null };
  }
};

export const foodAPI = {
  getCategories: async (): Promise<ApiResponse<Category[]>> => {
    await delay(500);
    return { success: true, data: DUMMY_CATEGORIES };
  },

  getBanners: async (): Promise<ApiResponse<Banner[]>> => {
    await delay(500);
    return { success: true, data: DUMMY_BANNERS };
  },

  getRestaurant: async (): Promise<ApiResponse<Restaurant>> => {
    await delay(500);
    return { success: true, data: RESTAURANT_INFO };
  },

  getFoodItems: async (category?: string): Promise<ApiResponse<FoodItem[]>> => {
    await delay(800);
    let items = DUMMY_FOOD_ITEMS;
    
    if (category) {
      items = items.filter(item => item.category.toLowerCase() === category.toLowerCase());
    }
    
    return { success: true, data: items };
  },

  getFoodItem: async (id: string): Promise<ApiResponse<FoodItem>> => {
    await delay(500);
    const item = DUMMY_FOOD_ITEMS.find(item => item.id === id);
    
    if (item) {
      return { success: true, data: item };
    }
    
    return {
      success: false,
      data: null as any,
      error: 'Food item not found'
    };
  },

  searchFoodItems: async (query: string): Promise<ApiResponse<FoodItem[]>> => {
    await delay(600);
    const results = DUMMY_FOOD_ITEMS.filter(item =>
      item.name.toLowerCase().includes(query.toLowerCase()) ||
      item.description.toLowerCase().includes(query.toLowerCase())
    );
    
    return { success: true, data: results };
  }
};

export const orderAPI = {
  createOrder: async (orderData: any): Promise<ApiResponse<Order>> => {
    await delay(1000);
    
    const order: Order = {
      id: Date.now().toString(),
      userId: '1',
      items: orderData.items,
      restaurant: orderData.restaurant,
      status: 'pending',
      total: orderData.total,
      deliveryFee: orderData.deliveryFee,
      tax: orderData.tax,
      grandTotal: orderData.grandTotal,
      deliveryAddress: orderData.deliveryAddress,
      paymentMethod: orderData.paymentMethod,
      orderDate: new Date().toISOString(),
      estimatedDeliveryTime: new Date(Date.now() + 30 * 60 * 1000).toISOString()
    };
    
    return { success: true, data: order };
  },

  getOrders: async (userId: string): Promise<ApiResponse<Order[]>> => {
    await delay(800);
    // Return empty array for now - in real app, this would fetch user's orders
    return { success: true, data: [] };
  },

  getOrder: async (orderId: string): Promise<ApiResponse<Order>> => {
    await delay(500);
    // Simulate order not found for now
    return {
      success: false,
      data: null as any,
      error: 'Order not found'
    };
  }
};
