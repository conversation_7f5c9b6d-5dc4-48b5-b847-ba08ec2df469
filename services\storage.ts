import AsyncStorage from '@react-native-async-storage/async-storage';
import { User, CartItem } from '@/types';

const STORAGE_KEYS = {
  USER: '@food_delivery_user',
  AUTH_TOKEN: '@food_delivery_token',
  CART: '@food_delivery_cart',
  RECENT_SEARCHES: '@food_delivery_recent_searches',
  FAVORITE_RESTAURANTS: '@food_delivery_favorites',
  USER_PREFERENCES: '@food_delivery_preferences'
};

export const storageService = {
  // User Authentication
  async saveUser(user: User): Promise<void> {
    try {
      await AsyncStorage.setItem(STORAGE_KEYS.USER, JSON.stringify(user));
    } catch (error) {
      console.error('Error saving user:', error);
    }
  },

  async getUser(): Promise<User | null> {
    try {
      const userData = await AsyncStorage.getItem(STORAGE_KEYS.USER);
      return userData ? JSON.parse(userData) : null;
    } catch (error) {
      console.error('Error getting user:', error);
      return null;
    }
  },

  async removeUser(): Promise<void> {
    try {
      await AsyncStorage.removeItem(STORAGE_KEYS.USER);
    } catch (error) {
      console.error('Error removing user:', error);
    }
  },

  // Auth Token
  async saveAuthToken(token: string): Promise<void> {
    try {
      await AsyncStorage.setItem(STORAGE_KEYS.AUTH_TOKEN, token);
    } catch (error) {
      console.error('Error saving auth token:', error);
    }
  },

  async getAuthToken(): Promise<string | null> {
    try {
      return await AsyncStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
    } catch (error) {
      console.error('Error getting auth token:', error);
      return null;
    }
  },

  async removeAuthToken(): Promise<void> {
    try {
      await AsyncStorage.removeItem(STORAGE_KEYS.AUTH_TOKEN);
    } catch (error) {
      console.error('Error removing auth token:', error);
    }
  },

  // Cart Management
  async saveCart(cartItems: CartItem[]): Promise<void> {
    try {
      await AsyncStorage.setItem(STORAGE_KEYS.CART, JSON.stringify(cartItems));
    } catch (error) {
      console.error('Error saving cart:', error);
    }
  },

  async getCart(): Promise<CartItem[]> {
    try {
      const cartData = await AsyncStorage.getItem(STORAGE_KEYS.CART);
      return cartData ? JSON.parse(cartData) : [];
    } catch (error) {
      console.error('Error getting cart:', error);
      return [];
    }
  },

  async clearCart(): Promise<void> {
    try {
      await AsyncStorage.removeItem(STORAGE_KEYS.CART);
    } catch (error) {
      console.error('Error clearing cart:', error);
    }
  },

  // Recent Searches
  async saveRecentSearch(query: string): Promise<void> {
    try {
      const recentSearches = await this.getRecentSearches();
      const updatedSearches = [query, ...recentSearches.filter(s => s !== query)].slice(0, 10);
      await AsyncStorage.setItem(STORAGE_KEYS.RECENT_SEARCHES, JSON.stringify(updatedSearches));
    } catch (error) {
      console.error('Error saving recent search:', error);
    }
  },

  async getRecentSearches(): Promise<string[]> {
    try {
      const searchData = await AsyncStorage.getItem(STORAGE_KEYS.RECENT_SEARCHES);
      return searchData ? JSON.parse(searchData) : [];
    } catch (error) {
      console.error('Error getting recent searches:', error);
      return [];
    }
  },

  async clearRecentSearches(): Promise<void> {
    try {
      await AsyncStorage.removeItem(STORAGE_KEYS.RECENT_SEARCHES);
    } catch (error) {
      console.error('Error clearing recent searches:', error);
    }
  },

  // Favorite Restaurants
  async saveFavoriteRestaurant(restaurantId: string): Promise<void> {
    try {
      const favorites = await this.getFavoriteRestaurants();
      if (!favorites.includes(restaurantId)) {
        const updatedFavorites = [...favorites, restaurantId];
        await AsyncStorage.setItem(STORAGE_KEYS.FAVORITE_RESTAURANTS, JSON.stringify(updatedFavorites));
      }
    } catch (error) {
      console.error('Error saving favorite restaurant:', error);
    }
  },

  async removeFavoriteRestaurant(restaurantId: string): Promise<void> {
    try {
      const favorites = await this.getFavoriteRestaurants();
      const updatedFavorites = favorites.filter(id => id !== restaurantId);
      await AsyncStorage.setItem(STORAGE_KEYS.FAVORITE_RESTAURANTS, JSON.stringify(updatedFavorites));
    } catch (error) {
      console.error('Error removing favorite restaurant:', error);
    }
  },

  async getFavoriteRestaurants(): Promise<string[]> {
    try {
      const favoritesData = await AsyncStorage.getItem(STORAGE_KEYS.FAVORITE_RESTAURANTS);
      return favoritesData ? JSON.parse(favoritesData) : [];
    } catch (error) {
      console.error('Error getting favorite restaurants:', error);
      return [];
    }
  },

  // User Preferences
  async saveUserPreferences(preferences: any): Promise<void> {
    try {
      await AsyncStorage.setItem(STORAGE_KEYS.USER_PREFERENCES, JSON.stringify(preferences));
    } catch (error) {
      console.error('Error saving user preferences:', error);
    }
  },

  async getUserPreferences(): Promise<any> {
    try {
      const preferencesData = await AsyncStorage.getItem(STORAGE_KEYS.USER_PREFERENCES);
      return preferencesData ? JSON.parse(preferencesData) : {};
    } catch (error) {
      console.error('Error getting user preferences:', error);
      return {};
    }
  },

  // Clear All Data
  async clearAllData(): Promise<void> {
    try {
      await AsyncStorage.multiRemove(Object.values(STORAGE_KEYS));
    } catch (error) {
      console.error('Error clearing all data:', error);
    }
  }
};
