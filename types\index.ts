// User and Authentication Types
export interface User {
  id: string;
  name: string;
  email: string;
  phone: string;
  avatar?: string;
  address?: Address;
}

export interface Address {
  id: string;
  street: string;
  city: string;
  state: string;
  zipCode: string;
  isDefault: boolean;
}

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
}

// Food and Restaurant Types
export interface Restaurant {
  id: string;
  name: string;
  image: string;
  rating: number;
  deliveryTime: string;
  deliveryFee: number;
  cuisine: string[];
  isOpen: boolean;
  distance: string;
}

export interface Category {
  id: string;
  name: string;
  image: string;
  color: string;
}

export interface FoodItem {
  id: string;
  name: string;
  description: string;
  price: number;
  image: string;
  category: string;
  restaurant: Restaurant;
  rating: number;
  isVegetarian: boolean;
  isSpicy: boolean;
  preparationTime: string;
  ingredients: string[];
  nutritionInfo?: NutritionInfo;
}

export interface NutritionInfo {
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
}

// Cart Types
export interface CartItem {
  id: string;
  foodItem: FoodItem;
  quantity: number;
  specialInstructions?: string;
  addOns?: AddOn[];
}

export interface AddOn {
  id: string;
  name: string;
  price: number;
}

export interface CartState {
  items: CartItem[];
  total: number;
  deliveryFee: number;
  tax: number;
  grandTotal: number;
}

// Order Types
export interface Order {
  id: string;
  userId: string;
  items: CartItem[];
  restaurant: Restaurant;
  status: OrderStatus;
  total: number;
  deliveryFee: number;
  tax: number;
  grandTotal: number;
  deliveryAddress: Address;
  paymentMethod: PaymentMethod;
  orderDate: string;
  estimatedDeliveryTime: string;
  actualDeliveryTime?: string;
  trackingInfo?: TrackingInfo;
}

export type OrderStatus = 
  | 'pending'
  | 'confirmed'
  | 'preparing'
  | 'ready'
  | 'picked_up'
  | 'on_the_way'
  | 'delivered'
  | 'cancelled';

export interface TrackingInfo {
  driverName: string;
  driverPhone: string;
  driverLocation: {
    latitude: number;
    longitude: number;
  };
  estimatedArrival: string;
}

export interface PaymentMethod {
  id: string;
  type: 'card' | 'cash' | 'digital_wallet';
  cardNumber?: string;
  cardHolderName?: string;
  expiryDate?: string;
  isDefault: boolean;
}

// Banner Types
export interface Banner {
  id: string;
  title: string;
  subtitle: string;
  image: string;
  actionUrl?: string;
  backgroundColor: string;
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Form Types
export interface LoginForm {
  email: string;
  password: string;
}

export interface RegisterForm {
  name: string;
  email: string;
  phone: string;
  password: string;
  confirmPassword: string;
}

export interface CheckoutForm {
  deliveryAddress: Address;
  paymentMethod: PaymentMethod;
  specialInstructions?: string;
}

// Review Types
export interface Review {
  id: string;
  userId: string;
  userName: string;
  userAvatar?: string;
  foodItemId: string;
  orderId: string;
  rating: number;
  comment: string;
  date: string;
  helpful: number;
}

export interface ReviewForm {
  rating: number;
  comment: string;
}

// Navigation Types
export interface TabParamList {
  index: undefined;
  menu: undefined;
  cart: undefined;
  orders: undefined;
  profile: undefined;
}

export interface RootStackParamList {
  '(tabs)': undefined;
  '(auth)': undefined;
  'product/[id]': { id: string };
  checkout: undefined;
  'thank-you': undefined;
  'review/[orderId]': { orderId: string };
  '+not-found': undefined;
}
